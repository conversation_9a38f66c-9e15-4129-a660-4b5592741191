{"title": "外键约束配置", "description": "定义跨文件的数据关联约束，确保数据完整性", "version": "1.0.0", "lastUpdated": "2025-08-19T09:00:00Z", "constraints": [{"id": "customer_order_to_customer", "description": "客户订单必须关联到有效的客户", "sourceFile": "mes/customer-orders.json", "sourceField": "orders[].customerName", "targetFile": "crm/customers.json", "targetField": "data[].name", "required": true, "cascadeDelete": false, "validationRule": "EXACT_MATCH"}, {"id": "customer_order_to_customer_id", "description": "客户订单应该通过ID关联客户", "sourceFile": "mes/customer-orders.json", "sourceField": "orders[].customerId", "targetFile": "crm/customers.json", "targetField": "data[].id", "required": false, "cascadeDelete": false, "validationRule": "EXACT_MATCH", "note": "建议使用ID关联而不是名称关联"}, {"id": "production_order_to_customer_order", "description": "生产工单必须关联到有效的客户订单", "sourceFile": "mes/production-orders.json", "sourceField": "productionOrders[].customerOrderId", "targetFile": "mes/customer-orders.json", "targetField": "orders[].id", "required": true, "cascadeDelete": true, "validationRule": "EXACT_MATCH"}, {"id": "production_order_item_to_customer_order_item", "description": "生产工单项必须关联到有效的客户订单项", "sourceFile": "mes/production-orders.json", "sourceField": "productionOrders[].items[].customerOrderItemId", "targetFile": "mes/customer-orders.json", "targetField": "orders[].items[].id", "required": true, "cascadeDelete": true, "validationRule": "EXACT_MATCH"}, {"id": "product_family_reference", "description": "产品族ID必须在产品族定义中存在", "sourceFile": "mes/customer-orders.json", "sourceField": "orders[].items[].productFamilyId", "targetFile": "masterdata/productFamilies.json", "targetField": "[].id", "required": true, "cascadeDelete": false, "validationRule": "EXACT_MATCH"}, {"id": "workstation_to_workcenter_mapping", "description": "工艺流程中的工作站必须能映射到工作中心", "sourceFile": "mes/customer-orders.json", "sourceField": "orders[].items[].processFlow[].workstation", "targetFile": "masterdata/workstationMapping.json", "targetField": "mappings.*", "required": true, "cascadeDelete": false, "validationRule": "KEY_EXISTS"}, {"id": "workcenter_reference", "description": "工作中心ID必须在工作中心定义中存在", "sourceFile": "masterdata/workstationMapping.json", "sourceField": "mappings.*.workCenterId", "targetFile": "masterdata/workCenters.json", "targetField": "[].id", "required": true, "cascadeDelete": false, "validationRule": "EXACT_MATCH"}], "enumConstraints": [{"id": "order_priority_enum", "description": "订单优先级必须使用标准枚举值", "sourceFile": "mes/customer-orders.json", "sourceField": "orders[].priority", "enumFile": "metadata/enums.json", "enumKey": "orderPriority", "required": true}, {"id": "order_status_enum", "description": "订单状态必须使用标准枚举值", "sourceFile": "mes/customer-orders.json", "sourceField": "orders[].status", "enumFile": "metadata/enums.json", "enumKey": "orderStatus", "required": true}, {"id": "work_order_status_enum", "description": "工单状态必须使用标准枚举值", "sourceFile": "mes/production-orders.json", "sourceField": "productionOrders[].status", "enumFile": "metadata/enums.json", "enumKey": "workOrderStatus", "required": true}, {"id": "glass_type_enum", "description": "玻璃类型必须使用标准枚举值", "sourceFile": "mes/customer-orders.json", "sourceField": "orders[].items[].specifications.glassType", "enumFile": "metadata/enums.json", "enumKey": "glassType", "required": true}, {"id": "customer_credit_level_enum", "description": "客户信用等级必须使用标准枚举值", "sourceFile": "crm/customers.json", "sourceField": "data[].creditLevel", "enumFile": "metadata/enums.json", "enumKey": "creditLevel", "required": true}], "businessRules": [{"id": "order_total_amount_calculation", "description": "订单项总金额必须等于数量乘以单价", "sourceFile": "mes/customer-orders.json", "rule": "orders[].items[].totalAmount === orders[].items[].quantity * orders[].items[].unitPrice", "severity": "error"}, {"id": "delivery_date_logic", "description": "交货日期必须晚于订单创建日期", "sourceFile": "mes/customer-orders.json", "rule": "orders[].items[].deliveryDate > orders[].createdAt", "severity": "warning"}, {"id": "production_order_date_logic", "description": "生产工单计划开始日期必须早于计划结束日期", "sourceFile": "mes/production-orders.json", "rule": "productionOrders[].plannedStartDate < productionOrders[].plannedEndDate", "severity": "error"}]}