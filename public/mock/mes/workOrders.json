{"scenario": "标准化工单数据", "description": "与production-orders.json保持一致的工单数据，统一日期格式和数据结构", "lastUpdated": "2025-08-19T09:00:00Z", "data": [{"id": "WO-2024-001", "workOrderNumber": "WO-2024-0001", "customerOrderId": "CO-2024-001", "customerOrderNumber": "华润-幕墙-240101", "customerName": "华润置地", "customerId": "CUST-001", "orderType": "幕墙工程", "priority": "high", "status": "pending", "plannedStartDate": "2025-08-21T00:00:00.000Z", "plannedEndDate": "2025-08-27T10:00:00.000Z", "createdAt": "2025-08-17T08:27:00.000Z", "processRoute": {"id": "route_001", "name": "幕墙工程加工路线", "steps": [{"id": "step_001", "name": "切割", "workCenter": "cold_processing", "workCenterId": "WC-CUT", "operation": "切割", "status": "pending", "duration": 15}, {"id": "step_002", "name": "磨边", "workCenter": "cold_processing", "workCenterId": "WC-EDGE", "operation": "磨边", "status": "pending", "duration": 25}, {"id": "step_003", "name": "镀膜", "workCenter": "coating", "workCenterId": "WC-COAT", "operation": "镀膜", "status": "pending", "duration": 30}, {"id": "step_004", "name": "钢化", "workCenter": "tempering", "workCenterId": "WC-TEMP", "operation": "钢化", "status": "pending", "duration": 45}, {"id": "step_005", "name": "质检", "workCenter": "quality_control", "workCenterId": "WC-QC", "operation": "质检", "status": "pending", "duration": 10}, {"id": "step_006", "name": "包装", "workCenter": "packaging", "workCenterId": "WC-PACK", "operation": "包装", "status": "pending", "duration": 5}]}}, {"id": "WO-2024-002", "workOrderNumber": "WO-2024-0002", "customerOrderId": "CO-2024-002", "customerOrderNumber": "万科-门窗-240102", "customerName": "万科集团", "customerId": "CUST-002", "orderType": "门窗工程", "priority": "normal", "status": "pending", "plannedStartDate": "2025-08-20T00:00:00.000Z", "plannedEndDate": "2025-09-04T10:00:00.000Z", "createdAt": "2025-08-14T03:02:00.000Z", "processRoute": {"id": "route_002", "name": "门窗工程加工路线", "steps": [{"id": "step_007", "name": "切割", "workCenter": "cold_processing", "workCenterId": "WC-CUT", "operation": "切割", "status": "pending", "duration": 10}, {"id": "step_008", "name": "磨边", "workCenter": "cold_processing", "workCenterId": "WC-EDGE", "operation": "磨边", "status": "pending", "duration": 18}, {"id": "step_009", "name": "钢化", "workCenter": "tempering", "workCenterId": "WC-TEMP", "operation": "钢化", "status": "pending", "duration": 35}, {"id": "step_010", "name": "质检", "workCenter": "quality_control", "workCenterId": "WC-QC", "operation": "质检", "status": "pending", "duration": 8}, {"id": "step_011", "name": "包装", "workCenter": "packaging", "workCenterId": "WC-PACK", "operation": "包装", "status": "pending", "duration": 5}]}}], "success": true, "message": "工单数据加载成功"}