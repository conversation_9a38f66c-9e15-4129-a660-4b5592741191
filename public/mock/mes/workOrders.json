{"data": [{"id": "wo_001", "workOrderNumber": "WO-2024-0001", "orderId": "CO-2024-001", "orderItemId": "COI-001", "customerName": "华润置地", "productType": "幕墙工程", "status": "pending", "priority": "high", "plannedStartDate": "2024-01-20T08:00:00Z", "plannedEndDate": "2024-01-25T18:00:00Z", "processRoute": {"id": "route_001", "name": "幕墙工程加工路线", "steps": [{"id": "step_001", "name": "切割", "workCenter": "cold_processing", "operation": "切割", "status": "pending", "duration": 120}, {"id": "step_002", "name": "磨边", "workCenter": "cold_processing", "operation": "磨边", "status": "pending", "duration": 120}, {"id": "step_003", "name": "镀膜", "workCenter": "coating", "operation": "镀膜", "status": "pending", "duration": 120}, {"id": "step_004", "name": "钢化", "workCenter": "tempering", "operation": "钢化", "status": "pending", "duration": 120}, {"id": "step_005", "name": "质检", "workCenter": "quality_control", "operation": "质检", "status": "pending", "duration": 120}, {"id": "step_006", "name": "包装", "workCenter": "packaging", "operation": "包装", "status": "pending", "duration": 120}]}, "createdAt": "2025-08-19T14:25:54.748Z"}], "success": true, "message": "工单数据加载成功"}